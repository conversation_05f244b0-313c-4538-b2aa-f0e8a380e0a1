development:
  prepared_statements: false
  adapter: postgresql
  host: <%= ENV['DISCOURSE_DB_HOST'] || '***********' %>
  port: <%= ENV['DISCOURSE_DB_PORT'] || '31597' %>
  database: <%= ENV['DISCOURSE_DB_NAME'] || 'bitnami_application' %>
  username: <%= ENV['DISCOURSE_DB_USERNAME'] || 'bn_discourse' %>
  password: <%= ENV['DISCOURSE_DB_PASSWORD'] || 'O*uelb&X8aMpt%$EcbU3yCdXm7TWd^Jr' %>
  min_messages: warning
  pool: 5
  checkout_timeout: <%= ENV['CHECKOUT_TIMEOUT'] || 5 %>
  host_names:
    ### Don't include the port number here. Change the "port" site setting instead, at /admin/site_settings.
    ### If you change this setting you will need to
    ###   - restart sidekiq if you change this setting
    ###   - rebake all to posts using: `RAILS_ENV=production bundle exec rake posts:rebake`
    - "<%= ENV['DISCOURSE_HOSTNAME'] || 'localhost' %>"
  variables: <%= ENV.filter { |k,v| k.start_with? 'DISCOURSE_DB_VARIABLES_' }
                    .transform_keys { |k| k.slice(('DISCOURSE_DB_VARIABLES_'.length)..).downcase }
                    .to_json %>

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.

<%
  test_db = ENV["RAILS_DB"]
  if !test_db.present?
    test_db = "discourse_test"

    if num = ENV["TEST_ENV_NUMBER"]
      num = num.presence || "1"
      test_db += "_#{num}"
    end
  end
%>

test:
  prepared_statements: false
  adapter: postgresql
  database: <%= test_db %>
  min_messages: warning
  pool: 2
  reaping_frequency: 0
  checkout_timeout: <%= ENV["CHECKOUT_TIMEOUT"] || 5 %>
  host_names:
    - test.localhost

# profile db is used for benchmarking using the script/bench.rb script
profile:
  prepared_statements: false
  adapter: postgresql
  database: discourse_profile
  min_messages: warning
  pool: 5
  host_names:
    - "localhost"
# You may be surprised production is not here, it is sourced from application.rb using a monkey patch
# This is done for 2 reasons
#
# 1. we need to support blank settings correctly and rendering nothing in yaml/erb is a PITA
# 2. why go from object -> yaml -> object, pointless
