/* eslint-disable ember/no-classic-components */
import Component from "@ember/component";
import {
  openLinkInNewTab,
  shouldOpenInNewTab,
} from "discourse/lib/click-track";

export default class HtmlWithLinks extends Component {
  click(event) {
    if (event?.target?.tagName === "A") {
      if (shouldOpenInNewTab(event.target.href)) {
        openLinkInNewTab(event, event.target);
      }
    }
  }
}
