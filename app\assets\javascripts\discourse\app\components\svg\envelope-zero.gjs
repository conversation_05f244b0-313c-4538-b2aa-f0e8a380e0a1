const SvgEnvelopeZero = <template>
  <svg
    width="259"
    height="211"
    viewBox="0 0 259 211"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M115.981 30.2207C121.745 26.3782 129.255 26.3782 135.019 30.2207L194.898 70.1406C196.289 71.0679 197.125 72.6291 197.125 74.3008C197.125 74.3008 197.125 75.4375 197.125 76.2187C197.125 77 194.5 78.5 193 79.5C191.5 80.5 190.5 81.5 189 82.1875C187.5 82.875 142 111.224 142 111.224C142 111.224 142 111.224 138.5 109C133.5 104.5 124.5 103.5 124.5 103.5C124.5 103.5 118.5 104.5 113 109L110 110.5L108.918 110.257C106.328 109.236 103.825 107.976 101.446 106.483L62.6943 82.1602C61 81 59.5 80 57.5 79C56.1016 78 55 77.5 53.9121 76.6484L53.875 76.625L53.9102 76.6357C53.8892 76.5142 53.875 76.3767 53.875 76.2187V74.3008C53.8751 72.6291 54.7106 71.0679 56.1016 70.1406L115.981 30.2207Z"
      fill="var(--tertiary-50)"
    />
    <path
      d="M196.5 74.3008C196.5 72.8381 195.769 71.4725 194.552 70.6611L134.672 30.7412C129.118 27.0385 121.882 27.0385 116.328 30.7412L56.4482 70.6611C55.2313 71.4725 54.5 72.8381 54.5 74.3008V149.219C54.5 150.636 55.0633 151.995 56.0654 152.997C57.0676 153.999 58.4265 154.563 59.8438 154.563H191.156C192.574 154.563 193.932 153.999 194.935 152.997C195.937 151.995 196.5 150.636 196.5 149.219V74.3008ZM197.75 149.219C197.75 150.968 197.055 152.644 195.818 153.881C194.582 155.117 192.905 155.813 191.156 155.813H59.8438C58.095 155.813 56.4182 155.117 55.1816 153.881C53.9451 152.644 53.25 150.968 53.25 149.219V74.3008C53.25 72.4202 54.1902 70.6643 55.7549 69.6211L115.635 29.7012C121.609 25.7185 129.391 25.7185 135.365 29.7012L195.245 69.6211C196.81 70.6643 197.75 72.4202 197.75 74.3008V149.219Z"
      fill="var(--tertiary-medium)"
    />
    <path
      d="M109.83 110.469L109.17 111.531L53.6699 77.0312L54.3301 75.9688L109.83 110.469Z"
      fill="var(--tertiary-medium)"
    />
    <path
      d="M141.67 110.469L142.33 111.531L197.5 77.2218L197.17 75.9688L141.67 110.469Z"
      fill="var(--tertiary-medium)"
    />
    <path
      d="M54.2295 149.921C53.9452 150.116 53.556 150.045 53.3604 149.761C53.1649 149.476 53.2365 149.087 53.5205 148.892L115.983 105.955C118.784 104.03 122.102 103 125.5 103C128.898 103 132.216 104.03 135.017 105.955L197.479 148.892L197.575 148.973C197.777 149.182 197.811 149.512 197.64 149.761C197.468 150.009 197.149 150.096 196.882 149.982L196.771 149.921L134.309 106.985C131.717 105.204 128.645 104.25 125.5 104.25C122.355 104.25 119.283 105.204 116.691 106.985L54.2295 149.921Z"
      fill="var(--tertiary-medium)"
    />
    <path
      d="M81.9307 169.438C82.4817 169.402 82.9574 169.82 82.9932 170.371C83.0289 170.922 82.6107 171.398 82.0596 171.434C80.0846 171.562 78.1692 171.623 76.3115 171.623C75.7592 171.623 75.3117 171.175 75.3115 170.623C75.3114 170.071 75.7584 169.623 76.3105 169.623C78.1253 169.623 79.9983 169.563 81.9307 169.438ZM65.1035 168.848L66.4619 169.027C67.8328 169.197 69.2431 169.334 70.6943 169.434C71.2452 169.471 71.6616 169.948 71.624 170.499C71.5862 171.05 71.1086 171.467 70.5576 171.429C69.0695 171.327 67.6226 171.187 66.2158 171.013L64.8232 170.827L64.7227 170.808C64.2305 170.686 63.901 170.21 63.9736 169.697C64.0463 169.185 64.4954 168.818 65.002 168.838L65.1035 168.848ZM93.3965 168.775L93.7021 169.728C92.5713 170.09 91.5475 170.351 90.6523 170.488C89.6865 170.637 88.7329 170.77 87.792 170.888C87.244 170.956 86.7444 170.567 86.6758 170.02C86.6072 169.472 86.9959 168.972 87.5439 168.903C88.4659 168.788 89.4004 168.657 90.3477 168.512C91.1043 168.395 92.0207 168.166 93.0918 167.822L93.3965 168.775ZM52.8779 167.119C53.0258 166.623 53.5245 166.327 54.0225 166.422L54.1221 166.446L55.4355 166.825C56.7644 167.195 58.1412 167.54 59.5674 167.852C60.1069 167.97 60.4489 168.503 60.3311 169.042C60.2204 169.548 59.7449 169.88 59.2412 169.822L59.1406 169.806L57.6934 169.475C56.7394 169.247 55.808 169.004 54.8984 168.751L53.5508 168.363L53.4541 168.329C52.9855 168.136 52.7301 167.615 52.8779 167.119ZM93.0918 167.822C93.6175 167.654 94.18 167.944 94.3486 168.47C94.5171 168.996 94.228 169.559 93.7021 169.728L93.0918 167.822ZM104.113 163.059C104.608 162.813 105.208 163.014 105.454 163.509C105.7 164.003 105.498 164.604 105.004 164.85C103.026 165.833 101.176 166.71 99.4736 167.465C98.9688 167.689 98.3781 167.461 98.1543 166.956C97.9308 166.451 98.1585 165.861 98.6631 165.637C100.332 164.897 102.155 164.032 104.113 163.059ZM42.2676 162.997C42.48 162.525 43.0141 162.298 43.4951 162.458L43.5898 162.495L44.833 163.044C45.6745 163.408 46.5425 163.769 47.4375 164.124L48.7998 164.651L48.8936 164.692C49.3475 164.918 49.5663 165.455 49.3838 165.939C49.1891 166.456 48.6116 166.717 48.0947 166.522C46.678 165.989 45.3267 165.437 44.0381 164.879L42.7695 164.319L42.6787 164.272C42.2399 164.019 42.0555 163.469 42.2676 162.997ZM114.842 157.405C115.326 157.14 115.933 157.317 116.199 157.801C116.465 158.285 116.289 158.892 115.805 159.158C113.961 160.171 112.179 161.135 110.466 162.041L110.373 162.084C109.903 162.275 109.356 162.082 109.114 161.624C108.856 161.136 109.042 160.531 109.53 160.272C111.233 159.372 113.006 158.414 114.842 157.405ZM32.084 157.896C32.3338 157.403 32.9353 157.206 33.4277 157.456C34.2165 157.856 35.0139 158.268 35.8486 158.699L38.4951 160.053L38.584 160.104C39.0112 160.376 39.1706 160.934 38.9375 161.397C38.7044 161.859 38.1616 162.062 37.6885 161.881L37.5947 161.839L34.9316 160.477L32.5234 159.24L32.4346 159.189C32.0078 158.916 31.8498 158.357 32.084 157.896ZM25 154.5C25.9792 154.5 27.0158 154.724 28.0967 155.083C28.6208 155.257 28.9045 155.824 28.7305 156.348C28.5564 156.871 27.9907 157.155 27.4668 156.981C26.4968 156.659 25.6856 156.5 25 156.5C24.4477 156.5 24 156.052 24 155.5C24 154.948 24.4477 154.5 25 154.5ZM125.434 151.439C125.912 151.164 126.524 151.329 126.799 151.808C127.074 152.287 126.909 152.898 126.43 153.173C124.626 154.209 122.857 155.218 121.129 156.192C120.648 156.464 120.038 156.294 119.767 155.813C119.495 155.331 119.665 154.722 120.146 154.45C121.869 153.479 123.634 152.473 125.434 151.439ZM135.934 145.327C136.41 145.047 137.023 145.207 137.303 145.683C137.583 146.159 137.424 146.772 136.948 147.052C135.179 148.092 133.428 149.117 131.702 150.122C131.225 150.4 130.613 150.238 130.335 149.761C130.057 149.284 130.219 148.671 130.696 148.394C132.419 147.39 134.166 146.367 135.934 145.327ZM142.185 143.961C141.709 144.242 141.096 144.085 140.814 143.61C140.533 143.135 140.691 142.522 141.166 142.24L142.185 143.961ZM146.483 139.09C146.943 138.876 147.499 139.041 147.764 139.486C148.046 139.961 147.89 140.575 147.415 140.857L142.185 143.961L141.166 142.24C142.896 141.216 144.64 140.18 146.394 139.138L146.483 139.09ZM156.932 132.865C157.391 132.651 157.947 132.815 158.212 133.26C158.495 133.734 158.34 134.348 157.865 134.631L152.642 137.745C152.167 138.028 151.553 137.873 151.271 137.398C150.988 136.924 151.143 136.31 151.617 136.027C153.355 134.992 155.098 133.953 156.842 132.913L156.932 132.865ZM167.296 126.69C167.771 126.409 168.384 126.566 168.666 127.041C168.948 127.516 168.791 128.129 168.316 128.411C166.581 129.441 164.837 130.477 163.09 131.518C162.615 131.8 162.001 131.645 161.719 131.17C161.436 130.695 161.592 130.081 162.066 129.799C163.815 128.758 165.559 127.721 167.296 126.69ZM177.775 120.51C178.252 120.231 178.865 120.392 179.144 120.868C179.423 121.345 179.263 121.957 178.786 122.236L173.548 125.315C173.072 125.596 172.459 125.438 172.179 124.962C171.898 124.486 172.056 123.874 172.531 123.593C174.294 122.553 176.043 121.524 177.775 120.51ZM188.392 114.375C188.855 114.169 189.407 114.344 189.664 114.794C189.938 115.274 189.771 115.885 189.291 116.158L186.683 117.652C185.806 118.156 184.923 118.666 184.033 119.181C183.555 119.457 182.943 119.294 182.666 118.816C182.39 118.339 182.553 117.727 183.031 117.45C184.814 116.418 186.573 115.407 188.301 114.421L188.392 114.375ZM198.957 108.47C199.442 108.206 200.05 108.385 200.313 108.87C200.577 109.355 200.398 109.963 199.913 110.227L197.288 111.665C196.401 112.155 195.501 112.655 194.591 113.164L194.499 113.209C194.034 113.41 193.482 113.231 193.229 112.779C192.96 112.297 193.133 111.688 193.615 111.418L196.321 109.914C197.212 109.423 198.091 108.941 198.957 108.47ZM209.876 102.794C210.351 102.618 210.892 102.827 211.12 103.292C211.363 103.788 211.159 104.387 210.663 104.63L209.36 105.275C208.04 105.935 206.668 106.637 205.25 107.378L205.157 107.421C204.687 107.609 204.14 107.414 203.9 106.955C203.645 106.466 203.835 105.861 204.324 105.605L205.736 104.873C207.133 104.153 208.483 103.472 209.781 102.835L209.876 102.794ZM218.132 99.0703C218.645 98.8671 219.226 99.1186 219.43 99.6319C219.633 100.145 219.381 100.726 218.868 100.93C217.996 101.276 217.073 101.663 216.103 102.089L216.007 102.125C215.524 102.281 214.993 102.048 214.785 101.574C214.563 101.069 214.793 100.479 215.299 100.257C216.288 99.8229 217.234 99.4264 218.132 99.0703Z"
      fill="var(--tertiary)"
    />
    <path
      d="M125 190C178.019 190 221 186.866 221 183C221 179.134 178.019 176 125 176C71.9807 176 29 179.134 29 183C29 186.866 71.9807 190 125 190Z"
      fill="var(--primary-low)"
    />
    <path
      d="M240.118 90.4745L232.711 100.228C232.547 100.47 232.268 100.618 231.967 100.621C231.812 100.603 231.656 100.585 231.502 100.501C231.4 100.446 231.31 100.365 231.235 100.258L228.816 97.2548L226.452 98.4951C226.253 98.6191 226.031 98.5988 225.852 98.5015C225.569 98.3487 225.464 97.9929 225.617 97.7107L226.951 95.2488C227.061 95.0436 227.21 94.8919 227.381 94.8192L236.863 90.6675L225.642 93.3786L223.597 90.8447C223.384 90.5636 223.318 90.196 223.499 89.8626C223.668 89.4898 223.96 89.3166 224.326 89.3157L239.358 89.0008C239.698 88.9859 240.058 89.1804 240.217 89.4988C240.376 89.8173 240.338 90.1945 240.118 90.4745Z"
      fill="var(--tertiary)"
    />
    <path
      d="M169 63.8334C178.389 63.8334 186 55.588 186 45.4167C186 35.2454 178.389 27 169 27C159.611 27 152 35.2454 152 45.4167C152 55.588 159.611 63.8334 169 63.8334Z"
      fill="var(--primary-low)"
    />
    <path
      d="M168.883 35.2708C170.963 35.2708 172.589 36.013 173.761 37.4974C175.157 39.2552 175.855 42.1702 175.855 46.2425C175.855 50.305 175.152 53.2249 173.746 55.0023C172.584 56.4671 170.963 57.1995 168.883 57.1995C166.793 57.1995 165.108 56.3987 163.829 54.7972C162.55 53.1859 161.91 50.3196 161.91 46.1985C161.91 42.1556 162.613 39.2454 164.02 37.4681C165.182 36.0032 166.803 35.2708 168.883 35.2708ZM168.883 38.6839C168.385 38.6839 167.94 38.845 167.55 39.1673C167.159 39.4798 166.856 40.0462 166.642 40.8665C166.358 41.931 166.217 43.723 166.217 46.2425C166.217 48.762 166.344 50.4954 166.598 51.4427C166.852 52.3802 167.169 53.0052 167.55 53.3177C167.94 53.6302 168.385 53.7864 168.883 53.7864C169.381 53.7864 169.825 53.6302 170.216 53.3177C170.606 52.9954 170.909 52.4241 171.124 51.6038C171.407 50.5491 171.549 48.762 171.549 46.2425C171.549 43.723 171.422 41.9944 171.168 41.0569C170.914 40.1097 170.592 39.4798 170.201 39.1673C169.82 38.845 169.381 38.6839 168.883 38.6839Z"
      fill="var(--primary)"
    />
    <path
      d="M56.849 45C56.849 45 58.219 48.191 59.588 49.659C61.066 51.243 63.698 51.849 63.698 51.849C63.698 51.849 61.066 52.544 59.588 54.128C58.219 55.597 56.849 58.698 56.849 58.698C56.849 58.698 55.479 55.597 54.109 54.128C52.632 52.544 50 51.849 50 51.849C50 51.849 52.848 51.243 54.326 49.659C55.695 48.191 56.849 45 56.849 45Z"
      fill="var(--tertiary)"
    />
    <path
      d="M206.01 140.816C206.01 140.816 206.826 142.375 207.576 143.063C208.386 143.806 209.738 144.009 209.738 144.009C209.738 144.009 208.436 144.463 207.751 145.321C207.116 146.117 206.547 147.738 206.547 147.738C206.547 147.738 205.733 146.225 204.983 145.536C204.175 144.794 202.816 144.546 202.816 144.546C202.816 144.546 204.232 144.128 204.917 143.27C205.552 142.473 206.01 140.816 206.01 140.816Z"
      fill="var(--tertiary)"
    />
  </svg>
</template>;

export default SvgEnvelopeZero;
