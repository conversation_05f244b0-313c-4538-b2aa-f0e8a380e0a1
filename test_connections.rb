#!/usr/bin/env ruby
# frozen_string_literal: true

# 测试数据库和 Redis 连接的脚本

puts "🔍 测试 Discourse 配置连接..."

# 显示环境变量
puts "\n🌍 相关环境变量:"
discourse_env_vars = ENV.select { |key, _| key.start_with?('DISCOURSE_') }
if discourse_env_vars.empty?
  puts "  ❌ 没有找到 DISCOURSE_ 环境变量!"
else
  discourse_env_vars.each do |key, value|
    puts "  #{key}: #{value}"
  end
end

# 加载 Rails 环境
puts "\n📚 加载 Rails 环境..."
begin
  require_relative 'config/environment'
  puts "✅ Rails 环境加载成功!"
rescue => e
  puts "❌ Rails 环境加载失败: #{e.message}"
  exit 1
end

puts "\n📊 当前环境: #{Rails.env}"

# 检查配置文件
puts "\n📄 检查配置文件..."
discourse_conf_path = File.join(Rails.root, 'config', 'discourse.conf')
if File.exist?(discourse_conf_path)
  puts "✅ discourse.conf 文件存在"
  puts "文件内容:"
  File.readlines(discourse_conf_path).each_with_index do |line, i|
    puts "  #{i+1}: #{line.chomp}"
  end
else
  puts "❌ discourse.conf 文件不存在"
end

# 测试数据库连接
puts "\n🗄️  测试数据库连接..."
begin
  db_config = Rails.application.config.database_configuration[Rails.env]
  puts "数据库配置:"
  puts "  Host: #{db_config['host']}"
  puts "  Port: #{db_config['port']}"
  puts "  Database: #{db_config['database']}"
  puts "  Username: #{db_config['username']}"
  puts "  Password: #{db_config['password'] ? '[已设置]' : '[未设置]'}"

  # 测试连接
  result = ActiveRecord::Base.connection.execute("SELECT version()")
  puts "✅ 数据库连接成功!"
  puts "  PostgreSQL 版本: #{result.first['version']}"
rescue => e
  puts "❌ 数据库连接失败: #{e.message}"
  puts "  错误类型: #{e.class}"
end

# 测试 Redis 连接
puts "\n🔴 测试 Redis 连接..."
begin
  redis_config = GlobalSetting.redis_config
  puts "Redis 配置:"
  puts "  Host: #{redis_config[:host]}"
  puts "  Port: #{redis_config[:port]}"
  puts "  DB: #{redis_config[:db]}"
  puts "  Password: #{redis_config[:password] ? '[已设置]' : '[未设置]'}"

  # 测试连接
  result = Discourse.redis.ping
  puts "✅ Redis 连接成功! (#{result})"

  # 获取 Redis 信息
  info = Discourse.redis.info
  puts "  Redis 版本: #{info['redis_version']}"
rescue => e
  puts "❌ Redis 连接失败: #{e.message}"
  puts "  错误类型: #{e.class}"
end

# 检查 GlobalSetting provider
puts "\n⚙️  检查 GlobalSetting provider..."
begin
  provider = GlobalSetting.provider
  puts "Provider 类型: #{provider.class}"
  if provider.respond_to?(:data)
    puts "Provider 数据: #{provider.data.inspect}"
  end
rescue => e
  puts "❌ 检查 GlobalSetting provider 失败: #{e.message}"
end

puts "\n✨ 测试完成!"
