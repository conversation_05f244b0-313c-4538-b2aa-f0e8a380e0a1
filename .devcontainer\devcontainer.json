{
  "name": "Discourse",
  "image": "docker.io/discourse/discourse_dev:20250307-0016",
  "workspaceMount": "source=${localWorkspaceFolder},target=/workspace/discourse,type=bind",
  "workspaceFolder": "/workspace/discourse",
  "postStartCommand": "./.devcontainer/scripts/start.rb",
  "forwardPorts": [
    9292, // bin/unicorn
    3000, // bin/rails s
    4200, // ember-cli
    8025, // mailhog
    9229  // chrome remote debug
  ],
  "remoteUser": "discourse",
  "remoteEnv": {
    "RAILS_DEVELOPMENT_HOSTS": ".app.github.dev",
    "PGUSER": "discourse",
    "SELENIUM_FORWARD_DEVTOOLS_TO_PORT": "9229",
    "DISCOURSE_HOSTNAME": "localhost",
    "DISCOURSE_DB_HOST": "***********",
    "DISCOURSE_DB_PORT": "31597",
    "DISCOURSE_DB_USERNAME": "bn_discourse",
    "DISCOURSE_DB_PASSWORD": "O*uelb&X8aMpt%$EcbU3yCdXm7TWd^Jr",
    "DISCOURSE_DB_NAME": "bitnami_application",
    "DISCOURSE_DB_BACKUP_HOST": "***********",
    "DISCOURSE_DB_BACKUP_PORT": "31597",
    "DISCOURSE_REDIS_HOST": "***********",
    "DISCOURSE_REDIS_PORT": "31550",
    "DISCOURSE_REDIS_PASSWORD": "KUcttXBPBmLHaMT6%HOlSCcH8A7$%%Dx",
    "DISCOURSE_REDIS_DB": "0",
    "DISCOURSE_SMTP_ADDRESS": "smtp.qiye.aliyun.com",
    "DISCOURSE_SMTP_PORT": "25",
    "DISCOURSE_SMTP_USER_NAME": "<EMAIL>",
    "DISCOURSE_SMTP_PASSWORD": "mvX0Rgjv9qS8pyAR",
    "DISCOURSE_SMTP_ENABLE_START_TLS": "false",
    "DISCOURSE_SMTP_AUTHENTICATION": "login"
  },
  "mounts": [
    "source=${localWorkspaceFolderBasename}-node_modules,target=${containerWorkspaceFolder}/node_modules,type=volume",
    "source=${localWorkspaceFolderBasename}-pg,target=/shared/postgres_data,type=volume",
    "source=${localWorkspaceFolderBasename}-redis,target=/shared/redis,type=volume"
  ],
  "customizations": {
    "vscode": {
      "extensions": [
        "Shopify.ruby-lsp",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "lifeart.vscode-glimmer-syntax",
        "typed-ember.glint-vscode",
        "stylelint.vscode-stylelint"
      ]
    }
  }
}
