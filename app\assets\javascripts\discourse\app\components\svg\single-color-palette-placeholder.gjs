const SvgSingleColorPalettePlaceholder = <template>
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 558 171">
    <rect
      width="120"
      height="25.616"
      x="436.298"
      y="95.819"
      fill="var(--primary-low--preview, #e9e9e9)"
      rx="4"
    />
    <rect
      width="120"
      height="24.64"
      x="436.298"
      y="144.442"
      fill="var(--tertiary--preview, #08c)"
      rx="4"
    />
    <rect
      width="555.265"
      height="71.394"
      x=".22"
      y=".341"
      fill="var(--tertiary-low--preview, #d1f0ff)"
      rx="4"
    />
    <rect
      width="30"
      height="24.64"
      x="23.451"
      y="145.418"
      fill="var(--danger--preview, #c80001)"
      rx="4"
    />
    <rect
      width="30"
      height="24.64"
      x="88.578"
      y="145.418"
      fill="var(--success--preview, #090)"
      rx="4"
    />
    <rect
      width="30"
      height="24.64"
      x="153.704"
      y="145.418"
      fill="var(--love--preview, #fa6c8d)"
      rx="4"
    />
    <rect
      width="30"
      height="24.64"
      x="218.831"
      y="145.418"
      fill="var(--highlight--preview, #ffff4d)"
      rx="4"
    />
    <rect
      width="200.35"
      height="4.526"
      x="23.451"
      y="43.772"
      fill="var(--primary--preview, #222)"
      rx="2.263"
    />
    <rect
      width="333.847"
      height="4.526"
      x="23.451"
      y="66.295"
      fill="var(--primary--preview, #222)"
      rx="2.263"
    />
    <rect
      width="267.897"
      height="4.526"
      x="23.451"
      y="90.819"
      fill="var(--primary--preview, #222)"
      rx="2.263"
    />
    <rect
      width="299.961"
      height="4.526"
      x="23.451"
      y="116.342"
      fill="var(--primary--preview, #222)"
      rx="2.263"
    />
  </svg>
</template>;

export default SvgSingleColorPalettePlaceholder;
