{"name": "custom-proxy", "version": "1.0.0", "description": "Express.js middleware which injects ember-cli asset URLs into Discourse's HTML", "author": "Discourse", "license": "GPL-2.0-only", "keywords": ["ember-addon"], "ember-addon": {"before": ["broccoli-serve-files", "proxy-server-middleware"], "after": ["broccoli-watcher"]}, "devDependencies": {"clean-base-url": "^1.0.0", "express": "^5.1.0", "glob": "^11.0.3", "html-entities": "^2.6.0", "html-rewriter-wasm": "^0.4.1", "node-fetch": "^3.3.2"}, "engines": {"node": ">= 18", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "^9"}}