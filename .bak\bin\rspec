#!/usr/bin/env ruby
# frozen_string_literal: true

#
# This file was generated by <PERSON>undler.
#
# The application 'rspec' is installed as part of a gem, and
# this file is here to facilitate running it.
#

require "pathname"
ENV["BUNDLE_GEMFILE"] ||= File.expand_path("../../Gemfile", Pathname.new(__FILE__).realpath)

require "rubygems"
require "bundler/setup"

if ENV["LOAD_PLUGINS"].nil? && ARGV.any? { |pattern| pattern.include?("plugins/") }
  STDERR.puts "Detected plugin spec path, setting LOAD_PLUGINS to 1"
  ENV["LOAD_PLUGINS"] = "1"
end

ENV["PLAYWRIGHT_HEADLESS"] = "0" if ARGV.delete("--headful")

load Gem.bin_path("rspec-core", "rspec")
