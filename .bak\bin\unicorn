#!/usr/bin/env ruby
# frozen_string_literal: true

require 'pathname'
ENV['BUNDLE_GEMFILE'] ||= File.expand_path("../../Gemfile",
  Pathname.new(__FILE__).realpath)
RAILS_ROOT = File.expand_path("../../", Pathname.new(__FILE__).realpath)

if defined? Bundler
  STDERR.puts <<~MESSAGE
    WARNING: Using `bundle exec` to start the server is unnecessary, and will make startup slower. Use `bin/rails s` or `bin/unicorn`.
  MESSAGE
end

require 'rubygems'
require 'bundler/setup'
require 'digest'
require 'fileutils'

dev_mode = false

def ensure_cache_clean!
  _all_plugin_directories = Pathname.new(RAILS_ROOT + '/plugins').children.select(&:directory?)
  core_git_sha = `git rev-parse HEAD`.strip
  plugins_combined_git_sha = `git ls-files -s plugins | git hash-object --stdin`.strip
  client_locale_paths_digest =
    Digest::SHA1.hexdigest(Dir.glob("#{RAILS_ROOT}/plugins/*/config/locales/client.*.yml").join)
  super_sha =
    Digest::SHA1.hexdigest(core_git_sha + plugins_combined_git_sha + client_locale_paths_digest)
  hash_file = "#{RAILS_ROOT}/tmp/plugin-hash"

  old_hash = File.exist?(hash_file) ? File.read(hash_file) : nil

  if old_hash && old_hash != super_sha
    FileUtils.rm_rf("#{RAILS_ROOT}/tmp/cache")
  end

  FileUtils.mkdir_p(RAILS_ROOT + "/tmp")
  File.write(hash_file, super_sha)
end

# in development do some fussing around, to automate config
if !ARGV.include?("-E") &&
    !ARGV.include?("--env") &&
    (["development", "test"].include?(ENV["RAILS_ENV"]) || !ENV["RAILS_ENV"])

  dev_mode = true

  ARGV.push("-N")
  if !ARGV.include?("-c") && !ARGV.include?("--config-file")
    ARGV.push("-c")
    ARGV.push(File.expand_path("../../config/unicorn.conf.rb",
      Pathname.new(__FILE__).realpath))
  end

  # we do not want to listen on 2 ports, so lets fix it
  if (idx = ARGV.index("-p")) && (port = ARGV[idx + 1].to_i) > 0
    ENV["UNICORN_PORT"] ||= port.to_s
  end

  ENV["UNICORN_PORT"] ||= "9292"

  if ARGV.delete("-x")
    puts "Running without sidekiq"
    ENV["UNICORN_SIDEKIQS"] = "0"
  end

  ENV["UNICORN_SIDEKIQS"] ||= "1"

  ensure_cache_clean!
end

if ARGV.include?("--help")
  fork do
    load Gem.bin_path('unicorn', 'unicorn')
  end
  Process.wait
  puts "Extra Discourse Options:"
  puts "  -x                       run without sidekiq"
  exit
end

# this dev_mode hackery enables the following to be used to restart unicorn:
#
# pkill -USR2 -f 'ruby bin/unicorn'
#
# This is handy if you want to bind a key to restarting unicorn in dev

if dev_mode
  UNICORN_DEV_SUPERVISOR_PID = Process.pid

  restart = true
  while restart
    restart = false
    pid = fork do
      load Gem.bin_path('unicorn', 'unicorn')
    end
    done = false

    Signal.trap('INT') do
      # wait for parent to be done
    end

    Signal.trap('USR2') do
      Process.kill('QUIT', pid)
      puts "RESTARTING UNICORN"
      restart = true
    end

    Signal.trap("TERM") do
      Process.kill('TERM', pid)
    end

    while !done
      sleep 1
      done = Process.waitpid(pid, Process::WNOHANG)
    end
  end
else
  load Gem.bin_path('unicorn', 'unicorn')
end
