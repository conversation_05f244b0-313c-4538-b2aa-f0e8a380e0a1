import { hash } from "@ember/helper";
import RouteTemplate from "ember-route-template";
import DPageSubheader from "discourse/components/d-page-subheader";
import Form from "discourse/components/form";
import { i18n } from "discourse-i18n";
import UserChooser from "select-kit/components/user-chooser";

export default RouteTemplate(
  <template>
    <DPageSubheader @titleLabel={{i18n "admin.embedding.posts_and_topics"}} />

    <Form
      @onSubmit={{@controller.save}}
      @data={{@controller.formData}}
      as |form|
    >
      <form.Field
        @name="embed_by_username"
        @title={{i18n "admin.embedding.embed_by_username"}}
        @validation="required"
        as |field|
      >
        <field.Custom>
          <UserChooser
            @value={{field.value}}
            @onChange={{field.set}}
            @options={{hash maximum=1 excludeCurrentUser=false}}
            class="admin-embedding-posts-and-topics-form__embed_by_username"
          />
        </field.Custom>
      </form.Field>
      <form.Field
        @name="embed_post_limit"
        @title={{i18n "admin.embedding.embed_post_limit"}}
        @format="large"
        as |field|
      >
        <field.Input />
      </form.Field>
      <form.Field
        @name="embed_title_scrubber"
        @title={{i18n "admin.embedding.embed_title_scrubber"}}
        @format="large"
        as |field|
      >
        <field.Input placeholder="- site.com$" />
      </form.Field>
      <form.CheckboxGroup as |checkboxGroup|>
        <checkboxGroup.Field
          @name="embed_truncate"
          @title={{i18n "admin.embedding.embed_truncate"}}
          as |field|
        >
          <field.Checkbox />
        </checkboxGroup.Field>

        <checkboxGroup.Field
          @name="embed_unlisted"
          @title={{i18n "admin.embedding.embed_unlisted"}}
          as |field|
        >
          <field.Checkbox />
        </checkboxGroup.Field>
      </form.CheckboxGroup>
      <form.Submit @label="admin.embedding.save" />
    </Form>
  </template>
);
